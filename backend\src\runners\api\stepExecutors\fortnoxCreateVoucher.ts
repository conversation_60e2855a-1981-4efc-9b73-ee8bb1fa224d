import axios from 'axios';
import OpenAI from 'openai';
import type { FortnoxCreateVoucherStep } from '@rpa-project/shared/dist/esm/types/steps/api';
import { StepExecutionResult } from '../../base';
import { customerService } from '../../../services/customerService';

/**
 * Executor context for Fortnox steps
 */
export interface FortnoxExecutorContext {
  variables: Record<string, any>;
  onLog: (log: { level: 'info' | 'warn' | 'error'; message: string; stepId?: string }) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  customerId?: string;
  openai?: OpenAI;
}

/**
 * Fortnox API account interface
 */
interface FortnoxAccount {
  Number: string;
  Description: string;
  Active: boolean;
  BalanceBroughtForward?: number;
  BalanceCarriedForward?: number;
  SRU?: number;
  Year?: number;
}

/**
 * Fortnox API chart of accounts response
 */
interface FortnoxAccountsResponse {
  Accounts: FortnoxAccount[];
}

/**
 * Fortnox voucher row interface
 */
interface FortnoxVoucherRow {
  Account: string;
  Debit?: number;
  Credit?: number;
  Description?: string;
}

/**
 * Fortnox voucher interface
 */
interface FortnoxVoucher {
  Description?: string;
  TransactionDate?: string;
  VoucherSeries?: string;
  VoucherRows: FortnoxVoucherRow[];
}

/**
 * AI response interface for voucher rows
 */
interface AIVoucherRowsResponse {
  rows: Array<{
    account: string;
    debit?: number;
    credit?: number;
    description?: string;
  }>;
  explanation?: string;
}

/**
 * Execute Fortnox Create Voucher step
 */
export async function executeFortnoxCreateVoucher(
  step: FortnoxCreateVoucherStep,
  context: FortnoxExecutorContext
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables, customerId, openai } = context;

  try {
    onLog({
      level: 'info',
      message: `Executing Fortnox Create Voucher: ${step.id}`,
      stepId: step.id
    });

    // Check if OpenAI is available for AI processing
    if (!openai) {
      throw new Error('OpenAI client is required for AI-powered voucher creation');
    }

    // Get Fortnox token for the customer
    if (!customerId) {
      throw new Error('Customer ID is required for Fortnox API calls');
    }

    const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
    const fortnoxToken = fortnoxTokens.find(token => token.provider === 'Fortnox' && token.apiToken);

    if (!fortnoxToken || !fortnoxToken.apiToken) {
      throw new Error('No valid Fortnox token found for customer');
    }

    onLog({
      level: 'info',
      message: 'Found Fortnox token, proceeding with voucher creation...',
      stepId: step.id
    });

    // Get input data from variable
    const inputData = variables[step.inputVariable];
    if (inputData === undefined || inputData === null) {
      throw new Error(`Input variable '${step.inputVariable}' not found or is null`);
    }

    onLog({
      level: 'info',
      message: 'Sending data to AI for voucher row generation...',
      stepId: step.id
    });

    // Create system prompt for AI
    const systemPrompt = `Du är en expert på svensk bokföring och Fortnox-verifikationer. Din uppgift är att skapa korrekta verifikationsrader baserat på input-data.

VIKTIGA REGLER:
1. Verifikationen MÅSTE vara balanserad (totalt debet = totalt kredit)
2. Använd endast giltiga kontonummer enligt svensk kontoplan (BAS-kontoplanen)
3. Alla belopp måste vara positiva tal
4. Svara ENDAST med JSON i följande format:

{
  "rows": [
    {
      "account": "kontonummer",
      "debit": belopp_eller_null,
      "credit": belopp_eller_null,
      "description": "beskrivning"
    }
  ],
  "explanation": "kort förklaring av verifikationen"
}

INSTRUKTIONER:
Användaren kommer att ange vilka konton som ska användas i sin prompt. Följ användarens instruktioner för kontohantering.`;

    // Interpolate the AI prompt with variables
    const interpolatedPrompt = interpolateVariables(step.aiPrompt, variables);

    // Prepare input data as string
    const inputDataStr = typeof inputData === 'string' ? inputData : JSON.stringify(inputData, null, 2);

    // Send to AI for processing
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `Skapa verifikationsrader för följande data:

INPUT DATA:
${inputDataStr}

INSTRUKTIONER:
${interpolatedPrompt}

Skapa en korrekt balanserad verifikation enligt svensk bokföringssed.`
        }
      ],
      temperature: 0.1, // Lower temperature for more consistent output
      max_tokens: 2000
    });

    const aiResponse = completion.choices[0]?.message?.content;
    if (!aiResponse) {
      throw new Error('No response from AI');
    }

    onLog({
      level: 'info',
      message: 'AI response received, parsing voucher rows...',
      stepId: step.id
    });

    // Parse AI response
    let aiVoucherData: AIVoucherRowsResponse;
    try {
      // Clean the response to extract JSON
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.startsWith('```json')) {
        jsonStr = jsonStr.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (jsonStr.startsWith('```')) {
        jsonStr = jsonStr.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      aiVoucherData = JSON.parse(jsonStr);

      if (!aiVoucherData.rows || !Array.isArray(aiVoucherData.rows)) {
        throw new Error('AI response must contain a "rows" array');
      }

    } catch (parseError) {
      const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown JSON parsing error';
      onLog({
        level: 'error',
        message: `Failed to parse AI response as JSON: ${errorMessage}. Raw response: ${aiResponse.substring(0, 200)}...`,
        stepId: step.id
      });
      throw new Error(`Invalid JSON response from AI: ${errorMessage}`);
    }

    // Validate and convert AI response to Fortnox format
    const voucherRows: FortnoxVoucherRow[] = [];
    let totalDebit = 0;
    let totalCredit = 0;

    for (const aiRow of aiVoucherData.rows) {
      // Validate amounts
      if (aiRow.debit && aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} cannot have both debit and credit`);
      }

      if (!aiRow.debit && !aiRow.credit) {
        throw new Error(`Row for account ${aiRow.account} must have either debit or credit`);
      }

      // Validate account number format (basic validation)
      if (!aiRow.account || !/^\d+$/.test(aiRow.account)) {
        throw new Error(`Invalid account number format: ${aiRow.account}. Must be numeric.`);
      }

      const voucherRow: FortnoxVoucherRow = {
        Account: aiRow.account,
        Description: aiRow.description || interpolateVariables(step.description || '', variables)
      };

      if (aiRow.debit) {
        const debitAmount = parseFloat(aiRow.debit.toString());
        if (isNaN(debitAmount) || debitAmount <= 0) {
          throw new Error(`Invalid debit amount for account ${aiRow.account}: ${aiRow.debit}`);
        }
        voucherRow.Debit = debitAmount;
        totalDebit += debitAmount;
      }

      if (aiRow.credit) {
        const creditAmount = parseFloat(aiRow.credit.toString());
        if (isNaN(creditAmount) || creditAmount <= 0) {
          throw new Error(`Invalid credit amount for account ${aiRow.account}: ${aiRow.credit}`);
        }
        voucherRow.Credit = creditAmount;
        totalCredit += creditAmount;
      }

      voucherRows.push(voucherRow);

      onLog({
        level: 'info',
        message: `Added row: Account ${aiRow.account} - ${aiRow.debit ? `Debit: ${aiRow.debit}` : `Credit: ${aiRow.credit}`}`,
        stepId: step.id
      });
    }

    // Validate that debits equal credits
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      throw new Error(`Voucher is not balanced: Total debit (${totalDebit}) != Total credit (${totalCredit})`);
    }

    onLog({
      level: 'info',
      message: `AI generated ${voucherRows.length} balanced voucher rows. ${aiVoucherData.explanation || ''}`,
      stepId: step.id
    });

    // Create voucher
    const voucher: FortnoxVoucher = {
      Description: interpolateVariables(step.description || 'AI Generated Voucher', variables),
      TransactionDate: step.transactionDate || new Date().toISOString().split('T')[0],
      VoucherSeries: step.voucherSeries || 'A',
      VoucherRows: voucherRows
    };

    onLog({
      level: 'info',
      message: `Creating voucher with ${voucherRows.length} rows (Total: ${totalDebit})`,
      stepId: step.id
    });

    // Send voucher to Fortnox
    let voucherResponse;
    try {
      voucherResponse = await axios.post(
        'https://api.fortnox.se/3/vouchers',
        { Voucher: voucher },
        {
          headers: {
            'Authorization': `Bearer ${fortnoxToken.apiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
    } catch (apiError: any) {
      // Log detailed error information from Fortnox API
      const errorDetails = apiError.response?.data || apiError.message;
      const statusCode = apiError.response?.status;

      onLog({
        level: 'error',
        message: `Fortnox API error (${statusCode}): ${JSON.stringify(errorDetails, null, 2)}`,
        stepId: step.id
      });

      // Also log the voucher data that was sent
      onLog({
        level: 'error',
        message: `Voucher data sent to Fortnox: ${JSON.stringify({ Voucher: voucher }, null, 2)}`,
        stepId: step.id
      });

      throw new Error(`Fortnox API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
    }

    const createdVoucher = voucherResponse.data.Voucher;

    // Store voucher information in variables
    const variableName = step.variableName || 'var-fortnox-voucher';
    const voucherResult = {
      voucherNumber: createdVoucher.VoucherNumber,
      voucherSeries: createdVoucher.VoucherSeries,
      voucherId: createdVoucher.VoucherNumber,
      totalAmount: totalDebit,
      rowsCount: voucherRows.length,
      aiExplanation: aiVoucherData.explanation,
      fullResponse: createdVoucher
    };

    variables[variableName] = voucherResult;
    variables['var-fortnox-accounts'] = accounts;

    onLog({
      level: 'info',
      message: `Voucher created successfully: ${createdVoucher.VoucherSeries}${createdVoucher.VoucherNumber}`,
      stepId: step.id
    });

    return {
      success: true,
      variables: {
        [variableName]: voucherResult,
        'var-fortnox-accounts': accounts
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Error creating Fortnox voucher: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
